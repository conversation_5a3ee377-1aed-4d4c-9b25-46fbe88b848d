import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:io';

import 'package:sms_autofill/sms_autofill.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../utils/app_themes.dart';
import '../../widgets/navigation_bar.dart';
import '../../widgets/adaptive_logo.dart';
import '../onboarding/user_profile_screen.dart';
import '../../widgets/pin_code_fields.dart';
import '../../widgets/animated_background.dart';
import '../../services/auth/auth_service.dart';

import '../../services/login_service.dart';
import '../../features/auth/application/auth_notifier.dart';
import '../../services/connectivity_error_service.dart';

class AuthScreen extends ConsumerStatefulWidget {
  const AuthScreen({super.key});

  @override
  ConsumerState<AuthScreen> createState() => AuthScreenState();
}

class AuthScreenState extends ConsumerState<AuthScreen>
    with TickerProviderStateMixin {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();

  // Animation controllers
  late AnimationController _animationController; // For OTP transitions
  late AnimationController _backgroundController; // For background animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Auth services
  final AuthService _authService = AuthService();

  // Focus node for phone input field
  final FocusNode _phoneFocusNode = FocusNode();

  bool _isOTPSent = false;
  bool _isLoading = false;
  bool _isResendEnabled = false;
  int _resendCountdown = 30;
  bool _isPhoneValid = false; // Track if phone number is valid (10 digits)
  bool _isOtpValid = false; // Track if OTP is valid (6 digits)
  Timer? _resendTimer;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Initialize OTP transition animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Initialize background animation controller
    _backgroundController = AnimationController(
      vsync: this,
      duration: const Duration(
          seconds: 20), // Longer duration for continuous animation
    );

    // Make the background animation repeat continuously
    _backgroundController.repeat();

    // Create animations for OTP transitions
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );

    // Initialize SMS Autofill - only on physical devices
    if (!_isEmulator()) {
      SmsAutoFill().listenForCode();
    }

    // Add focus listener for phone field
    _phoneFocusNode.addListener(_handlePhoneFocusChange);
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _otpController.dispose();
    _animationController.dispose();
    _backgroundController.dispose();
    _resendTimer?.cancel();
    if (!_isEmulator()) {
      SmsAutoFill().unregisterListener();
    }
    // Remove focus listener for phone field
    _phoneFocusNode.removeListener(_handlePhoneFocusChange);
    _phoneFocusNode.dispose();
    super.dispose();
  }

  // Helper method to detect if running on an emulator
  bool _isEmulator() {
    try {
      if (Platform.isAndroid) {
        // Common Android emulator model names
        return Platform.operatingSystemVersion.toLowerCase().contains('sdk') ||
            Platform.operatingSystemVersion.toLowerCase().contains('emulator');
      } else if (Platform.isIOS) {
        // Common iOS simulator identifiers
        return Platform.operatingSystemVersion
            .toLowerCase()
            .contains('simulator');
      }
      return false;
    } catch (e) {
      // If we can't determine, assume it's an emulator to be safe
      return true;
    }
  }

  void _startResendTimer([int seconds = 30]) {
    setState(() {
      _isResendEnabled = false;
      _resendCountdown = seconds;
    });

    _resendTimer?.cancel(); // Cancel any existing timer
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return; // <-- Added return
      }

      if (_resendCountdown > 0) {
        setState(() {
          _resendCountdown--;
        });
      } else {
        setState(() {
          _isResendEnabled = true;
        });
        timer.cancel();
      }
    });
  }

  Future<void> _sendOTP() async {
    // Prevent multiple rapid taps
    if (_isLoading) return;

    // Validate phone number immediately
    if (!_validatePhoneNumber(_phoneController.text)) {
      setState(() {
        _errorMessage = "Please enter a valid 10-digit phone number";
      });
      return;
    }

    // Show loading state immediately - this provides instant visual feedback
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // Hide keyboard
    FocusScope.of(context).unfocus();

    // Show a temporary snackbar to indicate the request is being processed
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                    strokeWidth: 2, color: Colors.white)),
            SizedBox(width: 16),
            Text('Sending OTP...'),
          ],
        ),
        backgroundColor: const Color(0xFF34C759),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
      ),
    );

    try {
      // Use phone number without +91 prefix
      final phoneNumber = _phoneController.text;

      // Store the phone number and timestamp for verification
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_phone_number', phoneNumber);
      await prefs.setInt(
          'last_otp_time', DateTime.now().millisecondsSinceEpoch);

      // Use the unified auth service
      final response = await _authService.sendOtp(phoneNumber);

      if (response['success'] == true) {
        // Start the resend timer
        _startResendTimer();

        // Show OTP input
        setState(() {
          _isLoading = false;
          _isOTPSent = true;
        });

        // Animate the OTP input
        _animationController.forward();
      } else {
        setState(() {
          _isLoading = false;
          // Use the formatted error message from the response
          _errorMessage =
              _formatErrorMessage(response['message'] ?? 'Failed to send OTP');
        });

        // Check if it's a connectivity-related error
        if (mounted &&
            (response['error_code'] == 'TIMEOUT' ||
                response['error_code'] == 'CONNECTION_ERROR')) {
          // Show connectivity error page for network-related issues
          await ConnectivityErrorService.showConnectivityError(
            context,
            customMessage: response['error_code'] == 'TIMEOUT'
                ? 'The request is taking longer than expected. Please check your connection and try again.'
                : 'Unable to connect to the server. Please check your internet connection.',
            onRetry: () {
              Navigator.of(context).pop();
              _sendOTP(); // Retry sending OTP
            },
          );
        } else if (mounted && response['error_code'] == 'SERVER_ERROR') {
          // Show server error with SnackBar for non-connectivity issues
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.warning_amber_rounded, color: Colors.white),
                  SizedBox(width: 16),
                  Expanded(
                      child: Text(
                          'The Ecoplug server is currently experiencing technical difficulties. Please try again later.')),
                ],
              ),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 8),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
              margin: EdgeInsets.all(10),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = _formatErrorMessage("Error: $e");
        // Log the actual error for debugging
        debugPrint('Error sending OTP: $e');
      });
    }
  }

  Future<void> _resendOTP() async {
    if (!_isResendEnabled) return;

    // Prevent multiple rapid taps
    if (_isLoading) return;

    // Show loading state immediately - this provides instant visual feedback
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // Hide keyboard
    FocusScope.of(context).unfocus();

    // Show a temporary snackbar to indicate the request is being processed
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                    strokeWidth: 2, color: Colors.white)),
            SizedBox(width: 16),
            Text('Resending OTP...'),
          ],
        ),
        backgroundColor: const Color(0xFF34C759),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
      ),
    );

    try {
      // Use phone number without +91 prefix
      final phoneNumber = _phoneController.text;

      // Store the phone number and timestamp for verification
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_phone_number', phoneNumber);
      await prefs.setInt(
          'last_otp_time', DateTime.now().millisecondsSinceEpoch);

      // Use the unified auth service
      final response = await _authService.sendOtp(phoneNumber);

      if (mounted) {
        if (response['success'] == true) {
          // Start the resend timer
          _startResendTimer();

          setState(() {
            _isLoading = false;
          });

          // Show success snackbar
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('OTP resent to $phoneNumber'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(10),
            ),
          );

          // Reset the OTP field
          _otpController.clear();

          // Listen for SMS code again (only on physical devices)
          if (!_isEmulator()) {
            SmsAutoFill().listenForCode();
          }
        } else {
          setState(() {
            _isLoading = false;
            // Use the formatted error message from the response
            _errorMessage = _formatErrorMessage(
                response['message'] ?? 'Failed to resend OTP');
          });

          // Check if it's a connectivity-related error
          if (response['error_code'] == 'TIMEOUT' ||
              response['error_code'] == 'CONNECTION_ERROR') {
            // Show connectivity error page for network-related issues
            await ConnectivityErrorService.showConnectivityError(
              context,
              customMessage:
                  'Unable to resend OTP. Please check your internet connection and try again.',
              onRetry: () {
                Navigator.of(context).pop();
                _resendOTP(); // Retry resending OTP
              },
            );
          } else if (response['error_code'] == 'SERVER_ERROR') {
            // Show server error with SnackBar for non-connectivity issues
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.warning_amber_rounded, color: Colors.white),
                    SizedBox(width: 16),
                    Expanded(
                        child: Text(
                            'The EcoPlug server is currently experiencing technical difficulties. Please try again later.')),
                  ],
                ),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 5),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10)),
                margin: EdgeInsets.all(10),
                action: SnackBarAction(
                  label: 'OK',
                  textColor: Colors.white,
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                  },
                ),
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = _formatErrorMessage("Error: $e");
          // Log the actual error for debugging
          debugPrint('Error resending OTP: $e');
        });
      }
    }
  }

  Future<void> _verifyOTP() async {
    // Debug log
    debugPrint('\n=== VERIFY OTP CALLED ===');
    debugPrint('OTP: ${_otpController.text}');

    // Validate OTP
    if (!_validateOTP(_otpController.text)) {
      setState(() {
        _errorMessage = "Please enter a valid 6-digit OTP";
      });
      debugPrint('OTP validation failed');
      return;
    }

    // Show loading state immediately - this provides instant visual feedback
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    debugPrint('Loading state set to true');

    // Hide keyboard
    FocusScope.of(context).unfocus();

    // Show a temporary snackbar to indicate the request is being processed
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                    strokeWidth: 2, color: Colors.white)),
            SizedBox(width: 16),
            Text('Verifying OTP...'),
          ],
        ),
        backgroundColor: const Color(0xFF34C759),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
      ),
    );
    debugPrint('Verification snackbar shown');

    try {
      final phoneNumber = _phoneController.text;
      final otp = _otpController.text;

      debugPrint('Attempting to verify OTP: $otp for phone: $phoneNumber');

      // Save the OTP to SharedPreferences for debugging purposes
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('last_otp_value', otp);
        await prefs.setInt(
            'last_otp_time', DateTime.now().millisecondsSinceEpoch);
        debugPrint('OTP saved to SharedPreferences for debugging');
      } catch (e) {
        debugPrint('Error saving OTP to SharedPreferences: $e');
      }

      // Use the unified auth service
      final response = await _authService.verifyOtp(phoneNumber, otp);
      debugPrint('Verification response: $response');

      if (response['success'] == true &&
          (response['data'] != null || response['user'] != null)) {
        debugPrint('Verification successful!');
        // Get user data from either 'data' or 'user' field
        final userData = response['data'] ?? response['user'];
        debugPrint('User data: $userData');

        // Store the token
        final token = userData['token'];

        // Log the token for debugging
        debugPrint('Token: $token');

        // CRITICAL: Use comprehensive login service for fresh data guarantee
        final loginService = LoginService();
        final loginSuccess = await loginService.performLoginWithFreshData(
          token: token,
          userData: userData,
          ref: ref,
        );

        if (!loginSuccess) {
          debugPrint('❌ Login with fresh data failed');
          setState(() {
            _isLoading = false;
            _errorMessage = 'Login failed. Please try again.';
          });
          return;
        }

        debugPrint('✅ Login with fresh data completed successfully');

        // Update the Riverpod auth state
        try {
          // Use the auth provider to update the auth state
          await ref.read(authProvider.notifier).signInWithOtp(
                phoneNumber: phoneNumber,
                otp: otp,
              );
          debugPrint('Riverpod auth state updated successfully');
        } catch (e) {
          debugPrint('Error updating Riverpod auth state: $e');
          // Continue with the flow even if Riverpod update fails
        }

        // Check if user is new (no name or email)
        final bool isNewUser = userData['name'] == null ||
            userData['name'].toString().isEmpty ||
            userData['email'] == null ||
            userData['email'].toString().isEmpty;
        debugPrint('Is new user: $isNewUser');

        // Get user ID
        final String userId = userData['id']?.toString() ?? '';
        debugPrint('User ID: $userId');

        if (mounted) {
          if (isNewUser) {
            debugPrint('Navigating to onboarding screen...');
            // Navigate to onboarding screen for new users
            Navigator.pushReplacement(
              context,
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    UserProfileScreen(
                  userId: userId,
                  phoneNumber: phoneNumber,
                  token: token,
                ),
                transitionsBuilder:
                    (context, animation, secondaryAnimation, child) {
                  const begin = Offset(1.0, 0.0);
                  const end = Offset.zero;
                  const curve = Curves.easeOutQuint;

                  var tween = Tween(begin: begin, end: end)
                      .chain(CurveTween(curve: curve));
                  var offsetAnimation = animation.drive(tween);

                  return SlideTransition(
                    position: offsetAnimation,
                    child: child,
                  );
                },
                transitionDuration: const Duration(milliseconds: 500),
              ),
            );
          } else {
            debugPrint('Navigating to main navigation...');
            // Navigate to home screen for existing users
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const MainNavigation()),
            );
          }
        }
      } else {
        debugPrint('Verification failed: ${response['message']}');
        setState(() {
          _isLoading = false;
          // Use the formatted error message from the response
          _errorMessage = _formatErrorMessage(
              response['message'] ?? 'Failed to verify OTP');
        });

        // Check if it's a connectivity-related error
        if (mounted &&
            (response['error_code'] == 'TIMEOUT' ||
                response['error_code'] == 'CONNECTION_ERROR')) {
          // Show connectivity error page for network-related issues
          await ConnectivityErrorService.showConnectivityError(
            context,
            customMessage:
                'Unable to verify OTP. Please check your internet connection and try again.',
            onRetry: () {
              Navigator.of(context).pop();
              _verifyOTP(); // Retry verifying OTP
            },
          );
        } else if (mounted && response['error_code'] == 'SERVER_ERROR') {
          // Get retry_after value or default to 10 seconds
          final retryAfter = response['retry_after'] as int? ?? 10;
          debugPrint('Starting retry timer for $retryAfter seconds');

          // Start a retry timer
          _startResendTimer(retryAfter);

          // Set the error message from the response
          setState(() {
            _errorMessage = response['message'] ??
                'Server connection issue. Please try again later.';
          });

          // Show a helpful message about server issues
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.warning_amber_rounded, color: Colors.white),
                  SizedBox(width: 16),
                  Expanded(
                      child: Text(
                          'The EcoPlug server is currently experiencing technical difficulties. Please try again later.')),
                ],
              ),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 5),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
              margin: EdgeInsets.all(10),
              action: SnackBarAction(
                label: 'OK',
                textColor: Colors.white,
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                },
              ),
            ),
          );
          debugPrint('Server issue snackbar shown');
        }
      }
    } catch (e) {
      debugPrint('CRITICAL ERROR in _verifyOTP method: $e');
      debugPrint('Error type: ${e.runtimeType}');
      debugPrint('Stack trace: ${StackTrace.current}');

      if (mounted) {
        // Check if this is a connectivity-related error
        if (ConnectivityErrorService.isConnectivityError(e)) {
          // Show connectivity error page instead of generic error
          await ConnectivityErrorService.showConnectivityError(
            context,
            customMessage: 'Unable to verify OTP. Please check your internet connection and try again.',
            onRetry: () {
              Navigator.of(context).pop();
              _verifyOTP(); // Retry verifying OTP
            },
          );
        } else {
          setState(() {
            _isLoading = false;
            _errorMessage = _formatErrorMessage("Error: $e");
            // Log the actual error for debugging
            debugPrint('Error verifying OTP: $e');
          });
        }
      }
    }
  }

  bool _validatePhoneNumber(String value) {
    if (value.isEmpty) return false;
    if (value.length != 10) return false;
    return RegExp(r'^[0-9]+$').hasMatch(value);
  }

  bool _validateOTP(String value) {
    if (value.isEmpty) return false;
    if (value.length != 6) return false;
    return RegExp(r'^[0-9]+$').hasMatch(value);
  }

  /// Format error message for better user experience
  String _formatErrorMessage(String message) {
    debugPrint('🔍 Formatting error message: $message');

    // Handle ApiException format
    if (message.contains('ApiException:')) {
      final parts = message.split('ApiException: ');
      if (parts.length > 1) {
        message = parts[1];
        // Remove the code part if present
        if (message.contains(' (Code:')) {
          message = message.split(' (Code:')[0];
        }
      }
    }

    // Handle timeout errors with more helpful messages
    if (message.toLowerCase().contains('timeout') ||
        message.toLowerCase().contains('timed out') ||
        message.contains('TimeoutException')) {
      debugPrint('🕐 Timeout error detected in UI');
      return 'The request is taking longer than expected. Please check your internet connection and try again.';
    }

    // If the message contains technical details about connection reset
    if (message.contains('Connection reset by peer')) {
      debugPrint('🔄 Connection reset error detected in UI');
      return 'The server connection was interrupted. Please try again in a moment.';
    }

    // If the message contains technical details about socket exceptions
    else if (message.contains('SocketException')) {
      debugPrint('🌐 Socket error detected in UI');
      return 'Network error: Please check your internet connection and try again.';
    }

    // Handle connection errors
    else if (message.toLowerCase().contains('connection') &&
        (message.toLowerCase().contains('failed') ||
            message.toLowerCase().contains('refused') ||
            message.toLowerCase().contains('error'))) {
      debugPrint('🌐 Connection error detected in UI');
      return 'Unable to connect to the server. Please check your internet connection or try again later.';
    }

    // Handle network errors
    else if (message.toLowerCase().contains('network') ||
        message.toLowerCase().contains('internet')) {
      debugPrint('📡 Network error detected in UI');
      return 'Network error. Please check your internet connection and try again.';
    }

    // If the message is too long, truncate it
    else if (message.length > 150) {
      return '${message.substring(0, 150)}...';
    }

    debugPrint('✅ Formatted error message: $message');
    return message;
  }

  // Handle focus changes for phone field
  void _handlePhoneFocusChange() {
    // Update validation when focus changes
    if (!_phoneFocusNode.hasFocus) {
      _validatePhoneNumber(_phoneController.text);
    }
  }

  Widget buildPhoneInput({required bool isDarkMode}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Enter your phone number',
          style: TextStyle(
            fontSize: 20, // Increased font size
            fontWeight: FontWeight.bold,
            color: isDarkMode ? AppThemes.darkTextPrimary : Colors.black87,
          ),
        ),
        const SizedBox(height: 8), // Consistent small spacing
        Text(
          'We\'ll send you a verification code',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500, // Slightly bolder
            color: isDarkMode
                ? Colors.grey[300]
                : Colors.grey[700], // Improved contrast
            height: 1.3, // Better line height
          ),
        ),
        const SizedBox(height: 32), // Increased for better visual separation
        // Completely redesigned phone input field with single color
        Container(
          height: 56, // Standard height
          decoration: BoxDecoration(
            color: isDarkMode ? Color(0xFF1E1E1E) : Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: _isPhoneValid
                  ? const Color(0xFF67C44C) // Green when valid
                  : _phoneFocusNode.hasFocus
                      ? isDarkMode
                          ? AppThemes.darkerGreen
                          : AppThemes.primaryColor
                      : isDarkMode
                          ? Color(0xFF333333)
                          : Colors.grey.shade300,
              width: _isPhoneValid ? 2 : 1.5, // Thicker border when valid
            ),
            boxShadow: [
              BoxShadow(
                color: _isPhoneValid
                    ? const Color(0xFF67C44C)
                        .withAlpha(60) // Green shadow when valid
                    : _phoneFocusNode.hasFocus
                        ? isDarkMode
                            ? AppThemes.darkerGreen.withAlpha(40)
                            : AppThemes.primaryColor.withAlpha(60)
                        : Colors.transparent,
                blurRadius: 6,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Country code section with flag - compact
              Padding(
                padding: const EdgeInsets.only(left: 16),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Indian flag
                    const Text(
                      "🇮🇳",
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 4),
                    // Country code
                    Text(
                      '+91',
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
              // Spacer
              const SizedBox(width: 12),
              // Phone number input
              Expanded(
                child: TextField(
                  controller: _phoneController,
                  focusNode: _phoneFocusNode,
                  keyboardType: TextInputType.phone,
                  textAlignVertical: TextAlignVertical.center,
                  style: TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    color: isDarkMode ? Colors.white : Colors.black87,
                    letterSpacing: 0.5,
                  ),
                  maxLength: 10,
                  decoration: InputDecoration(
                    hintText: 'Phone number',
                    hintStyle: TextStyle(
                      color: isDarkMode
                          ? Colors.grey.shade500
                          : Colors.grey.shade500,
                      fontSize: 16,
                    ),
                    counterText: '',
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 16),
                    isDense: true,
                  ),
                  cursorColor: isDarkMode
                      ? AppThemes.darkerGreen
                      : AppThemes.primaryColor,
                  onChanged: (value) {
                    // Clear error message when user types
                    setState(() {
                      if (_errorMessage != null) {
                        _errorMessage = null;
                      }
                      // Update phone validity state
                      _isPhoneValid = value.length == 10 &&
                          RegExp(r'^[0-9]+$').hasMatch(value);
                    });

                    // Auto-dismiss keyboard when exactly 10 digits are entered
                    if (value.length == 10 &&
                        RegExp(r'^[0-9]+$').hasMatch(value)) {
                      FocusScope.of(context).unfocus();
                    }
                  },
                ),
              ),
              // Checkmark icon when valid
              if (_isPhoneValid)
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: Icon(
                    Icons.check_circle,
                    color: const Color(0xFF67C44C),
                    size: 24,
                  ),
                ),
              // Right padding
              const SizedBox(width: 16),
            ],
          ),
        ),
        // Removed extra spacing - handled in main layout
      ],
    );
  }

  Widget buildOTPInput({required bool isDarkMode}) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Enter verification code',
              style: TextStyle(
                fontSize: 20, // Consistent with phone input title
                fontWeight: FontWeight.bold,
                color: isDarkMode ? AppThemes.darkTextPrimary : Colors.black87,
              ),
            ),
            const SizedBox(height: 8), // Consistent small spacing
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'We\'ve sent a code to +91 ${_phoneController.text}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(width: 5),
                GestureDetector(
                  onTap: () {
                    // Go back to phone number input
                    setState(() {
                      _isOTPSent = false;
                      _otpController.clear();
                      _errorMessage = null;
                    });

                    // Reset animations
                    _animationController.reset();
                  },
                  child: const Text(
                    'Edit',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF34C759),
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(
                height: 32), // Increased for better visual separation
            Column(
              children: [
                PinCodeFields(
                  controller: _otpController,
                  length: 6,
                  onChanged: (value) {
                    // Clear error message when user types and update OTP validity
                    setState(() {
                      if (_errorMessage != null) {
                        _errorMessage = null;
                      }
                      // Update OTP validity state
                      _isOtpValid = value.length == 6 &&
                          RegExp(r'^[0-9]+$').hasMatch(value);
                    });
                  },
                  onCompleted: (value) {
                    // Auto-verify when all digits are entered
                    _verifyOTP();
                  },
                ),
                // Loading indicator during OTP verification
                if (_isLoading)
                  Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: isDarkMode
                                ? AppThemes.darkerGreen
                                : AppThemes.primaryColor,
                            strokeWidth: 2,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Verifying OTP...',
                          style: TextStyle(
                            fontSize: 14,
                            color: isDarkMode
                                ? Colors.grey[300]
                                : Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Stack(
        children: [
          // Animated background
          AnimatedBackground(
            primaryColor:
                isDarkMode ? AppThemes.darkerGreen : AppThemes.primaryColor,
            secondaryColor: isDarkMode ? Colors.teal : Colors.green,
          ),

          // Main content
          SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Login page compatible top spacing - follows golden ratio principle
                    SizedBox(height: MediaQuery.of(context).size.height * 0.12),

                    // Logo and app name section - centered and balanced
                    Center(
                      child: Column(
                        children: [
                          const AdaptiveLogo.medium(),
                          const SizedBox(
                              height: 20), // Optimal spacing for login pages
                          Text(
                            'Ecoplug',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                          const SizedBox(
                              height: 8), // Tight spacing for subtitle
                          Text(
                            'Charge your EV anywhere',
                            style: TextStyle(
                              fontSize: 16,
                              color: isDarkMode
                                  ? Colors.grey[300]
                                  : Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Login page standard spacing between header and form
                    SizedBox(height: MediaQuery.of(context).size.height * 0.08),

                    // Phone input or OTP input based on state
                    if (!_isOTPSent) buildPhoneInput(isDarkMode: isDarkMode),
                    if (_isOTPSent) buildOTPInput(isDarkMode: isDarkMode),

                    // Error message with improved spacing
                    if (_errorMessage != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(
                            color: Colors.red[400],
                            fontSize: 14,
                          ),
                        ),
                      ),

                    // Consistent spacing before action button
                    if (!_isOTPSent) const SizedBox(height: 24),

                    // Action button with proper spacing
                    if (!_isOTPSent)
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed:
                              _isPhoneValid && !_isLoading ? _sendOTP : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isDarkMode
                                ? AppThemes.darkerGreen
                                : AppThemes.primaryColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 0,
                            disabledBackgroundColor: isDarkMode
                                ? AppThemes.darkerGreen
                                    .withAlpha(76) // 0.3 * 255 = ~76
                                : AppThemes.primaryColor.withAlpha(76),
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Text(
                                  'Send OTP',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      ),

                    // Resend OTP button
                    if (_isOTPSent)
                      Padding(
                        padding: const EdgeInsets.only(top: 24.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Didn\'t receive the code? ',
                              style: TextStyle(
                                fontSize: 14,
                                color: isDarkMode
                                    ? Colors.grey[300]
                                    : Colors.grey[700],
                              ),
                            ),
                            _isResendEnabled
                                ? GestureDetector(
                                    onTap: _resendOTP,
                                    child: Text(
                                      'Resend',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: isDarkMode
                                            ? AppThemes.darkerGreen
                                            : AppThemes.primaryColor,
                                      ),
                                    ),
                                  )
                                : Text(
                                    'Resend in $_resendCountdown',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: isDarkMode
                                          ? Colors.grey[400]
                                          : Colors.grey[600],
                                    ),
                                  ),
                          ],
                        ),
                      ),

                    // Consistent bottom spacing for better layout balance
                    const SizedBox(height: 48),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
