import 'package:flutter/material.dart';
import 'dart:io';
import '../screens/error/connectivity_error_page.dart';
import 'connectivity_service.dart';

/// Service for handling connectivity errors and navigation to error page
class ConnectivityErrorService {
  // Singleton pattern
  static final ConnectivityErrorService _instance =
      ConnectivityErrorService._internal();
  factory ConnectivityErrorService() => _instance;
  ConnectivityErrorService._internal();

  final ConnectivityService _connectivityService = ConnectivityService();

  /// Check if an error is connectivity-related
  static bool isConnectivityError(dynamic error) {
    if (error == null) return false;

    final errorString = error.toString().toLowerCase();

    // Network-related errors
    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('socketexception') ||
        errorString.contains('timeout') ||
        errorString.contains('timed out') ||
        errorString.contains('connection reset') ||
        errorString.contains('connection refused') ||
        errorString.contains('no internet') ||
        errorString.contains('offline') ||
        errorString.contains('unreachable') ||
        errorString.contains('dns') ||
        errorString.contains('host lookup failed')) {
      return true;
    }

    // HTTP status codes that might indicate connectivity issues
    if (errorString.contains('408') || // Request Timeout
        errorString.contains('502') || // Bad Gateway
        errorString.contains('503') || // Service Unavailable
        errorString.contains('504') || // Gateway Timeout
        errorString.contains('522') || // Connection Timed Out
        errorString.contains('523') || // Origin Is Unreachable
        errorString.contains('524')) {
      // A Timeout Occurred
      return true;
    }

    // Check for specific exception types
    if (error is SocketException ||
        error is HttpException ||
        error is HandshakeException) {
      return true;
    }

    return false;
  }

  /// Get user-friendly error message for connectivity issues
  static String getConnectivityErrorMessage(dynamic error) {
    if (error == null)
      return 'Connection error occurred. Please check your internet connection.';

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('timeout') || errorString.contains('timed out')) {
      return 'Connection timed out. Please check your internet connection and try again.';
    }

    if (errorString.contains('dns') ||
        errorString.contains('host lookup failed')) {
      return 'Unable to reach the server. Please check your internet connection.';
    }

    if (errorString.contains('connection refused') ||
        errorString.contains('connection reset')) {
      return 'Connection was refused by the server. Please try again later.';
    }

    if (errorString.contains('502') ||
        errorString.contains('503') ||
        errorString.contains('504')) {
      return 'Server is temporarily unavailable. Please try again in a few moments.';
    }

    if (errorString.contains('offline') ||
        errorString.contains('no internet')) {
      return 'You appear to be offline. Please check your internet connection.';
    }

    // Default connectivity error message
    return 'Connection error occurred. Please check your internet connection and try again.';
  }

  /// Navigate to connectivity error page
  static Future<void> showConnectivityError(
    BuildContext context, {
    String? customMessage,
    VoidCallback? onRetry,
    VoidCallback? onGoBack,
    bool showBackButton = true,
    bool replaceCurrentRoute = false,
  }) async {
    if (!context.mounted) return;

    final errorPage = ConnectivityErrorPage(
      errorMessage: customMessage,
      onRetry: onRetry,
      onGoBack: onGoBack,
      showBackButton: showBackButton,
    );

    if (replaceCurrentRoute) {
      await Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => errorPage),
      );
    } else {
      await Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => errorPage),
      );
    }
  }

  /// Show connectivity error as a modal bottom sheet
  static Future<void> showConnectivityErrorSheet(
    BuildContext context, {
    String? customMessage,
    VoidCallback? onRetry,
  }) async {
    if (!context.mounted) return;

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: ConnectivityErrorPage(
          errorMessage: customMessage,
          onRetry: onRetry,
          onGoBack: () => Navigator.of(context).pop(),
          showBackButton: false,
        ),
      ),
    );
  }

  /// Handle API call with automatic connectivity error handling
  static Future<T> handleApiCall<T>(
    Future<T> Function() apiCall, {
    required BuildContext context,
    String? customErrorMessage,
    VoidCallback? onRetry,
    bool showErrorPage = true,
    bool showAsSheet = false,
  }) async {
    try {
      return await apiCall();
    } catch (error) {
      debugPrint('API call error: $error');

      if (isConnectivityError(error) && context.mounted && showErrorPage) {
        final errorMessage =
            customErrorMessage ?? getConnectivityErrorMessage(error);

        if (showAsSheet) {
          await showConnectivityErrorSheet(
            context,
            customMessage: errorMessage,
            onRetry: onRetry,
          );
        } else {
          await showConnectivityError(
            context,
            customMessage: errorMessage,
            onRetry: onRetry,
          );
        }
      }

      rethrow; // Re-throw the error for caller to handle if needed
    }
  }

  /// Check connectivity and show error page if no connection
  Future<bool> checkConnectivityAndShowError(
    BuildContext context, {
    String? customMessage,
    VoidCallback? onRetry,
    bool showAsSheet = false,
  }) async {
    try {
      await _connectivityService.checkConnectionManually();

      if (!_connectivityService.hasConnection && context.mounted) {
        final errorMessage = customMessage ??
            'No internet connection available. Please check your network settings and try again.';

        if (showAsSheet) {
          await showConnectivityErrorSheet(
            context,
            customMessage: errorMessage,
            onRetry: onRetry,
          );
        } else {
          await showConnectivityError(
            context,
            customMessage: errorMessage,
            onRetry: onRetry,
          );
        }

        return false;
      }

      return _connectivityService.hasConnection;
    } catch (error) {
      debugPrint('Error checking connectivity: $error');
      return false;
    }
  }

  /// Utility method to wrap any function with connectivity checking
  static Future<T?> withConnectivityCheck<T>(
    BuildContext context,
    Future<T> Function() operation, {
    String? errorMessage,
    bool showAsSheet = false,
  }) async {
    final service = ConnectivityErrorService();

    final hasConnection = await service.checkConnectivityAndShowError(
      context,
      customMessage: errorMessage,
      showAsSheet: showAsSheet,
    );

    if (!hasConnection) return null;

    try {
      return await operation();
    } catch (error) {
      if (isConnectivityError(error) && context.mounted) {
        final errorMsg = errorMessage ?? getConnectivityErrorMessage(error);

        if (showAsSheet) {
          await showConnectivityErrorSheet(context, customMessage: errorMsg);
        } else {
          await showConnectivityError(context, customMessage: errorMsg);
        }
      }
      rethrow;
    }
  }
}
