import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import '../../providers/theme_provider.dart';
import '../../utils/app_themes.dart';
import '../../services/connectivity_service.dart';

/// A dedicated error page for internet connectivity issues
/// Displays a Lottie animation and provides retry functionality
class ConnectivityErrorPage extends ConsumerStatefulWidget {
  final String? errorMessage;
  final VoidCallback? onRetry;

  const ConnectivityErrorPage({
    super.key,
    this.errorMessage,
    this.onRetry,
  });

  @override
  ConsumerState<ConnectivityErrorPage> createState() =>
      _ConnectivityErrorPageState();
}

class _ConnectivityErrorPageState extends ConsumerState<ConnectivityErrorPage>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isRetrying = false;
  final ConnectivityService _connectivityService = ConnectivityService();

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _handleRetry() async {
    if (_isRetrying) return;

    setState(() {
      _isRetrying = true;
    });

    try {
      // Check connectivity first
      await _connectivityService.checkConnectionManually();

      // Wait a moment for user feedback
      await Future.delayed(const Duration(milliseconds: 500));

      if (_connectivityService.hasConnection) {
        // Connection restored, execute retry callback or go back
        if (widget.onRetry != null) {
          widget.onRetry!();
        }
      } else {
        // Still no connection, show feedback
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                  'Still no internet connection. Please check your network settings.'),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error during retry: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                const Text('Unable to check connection. Please try again.'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRetrying = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = ref.watch(themeNotifierProvider.notifier).isDarkMode;

    return PopScope(
      canPop: false, // Prevent manual back navigation - only allow automatic dismissal
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        debugPrint('🌐 CONNECTIVITY ERROR PAGE: Manual back navigation blocked');
      },
      child: Scaffold(
        backgroundColor: isDarkMode ? AppThemes.darkBackground : Colors.white,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Lottie Animation
                  SizedBox(
                    height: 200,
                    width: 200,
                    child: Lottie.asset(
                      'assets/data/erorr Animation.json',
                      fit: BoxFit.contain,
                      repeat: true,
                      animate: true,
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Error Title
                  Text(
                    'No Internet Connection',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode
                          ? AppThemes.darkTextPrimary
                          : AppThemes.lightTextPrimary,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 16),

                  // Error Message
                  Text(
                    widget.errorMessage ??
                        'Please check your internet connection and try again. Make sure you\'re connected to Wi-Fi or mobile data.',
                    style: TextStyle(
                      fontSize: 16,
                      color: isDarkMode
                          ? AppThemes.darkTextSecondary
                          : AppThemes.lightTextSecondary,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 40),

                  // Action Buttons
                  Column(
                    children: [
                      // Retry Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _isRetrying ? null : _handleRetry,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppThemes.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child: _isRetrying
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : const Text(
                                  'Try Again',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),

                      const SizedBox(height: 12),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Connection Tips
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? AppThemes.darkCard.withOpacity(0.5)
                          : Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.grey.shade200,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              size: 20,
                              color: isDarkMode
                                  ? AppThemes.darkTextSecondary
                                  : AppThemes.lightTextSecondary,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Connection Tips',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: isDarkMode
                                    ? AppThemes.darkTextPrimary
                                    : AppThemes.lightTextPrimary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• Check if Wi-Fi or mobile data is enabled\n'
                          '• Try moving to an area with better signal\n'
                          '• Restart your router or mobile data\n'
                          '• Contact your internet service provider',
                          style: TextStyle(
                            fontSize: 13,
                            color: isDarkMode
                                ? AppThemes.darkTextSecondary
                                : AppThemes.lightTextSecondary,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
